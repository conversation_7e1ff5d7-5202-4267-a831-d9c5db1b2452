/*---------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from 'vs/base/common/lifecycle';
import { IViewPaneOptions, ViewPane } from 'vs/workbench/browser/parts/views/viewPane';
import { IKeybindingService } from 'vs/platform/keybinding/common/keybinding';
import { IContextMenuService } from 'vs/platform/contextview/browser/contextView';
import { IConfigurationService } from 'vs/platform/configuration/common/configuration';
import { IContextKeyService } from 'vs/platform/contextkey/common/contextkey';
import { IViewDescriptorService } from 'vs/workbench/common/views';
import { IInstantiationService } from 'vs/platform/instantiation/common/instantiation';
import { IOpenerService } from 'vs/platform/opener/common/opener';
import { ITelemetryService } from 'vs/platform/telemetry/common/telemetry';
import { IThemeService } from 'vs/platform/theme/common/themeService';
import { localize } from 'vs/nls';
import { ICommandService } from 'vs/platform/commands/common/commands';

export class AuraCanvasViewPane extends ViewPane {

	static readonly ID = 'aura.canvas';
	static readonly TITLE = localize('aura.canvas', "Component Canvas");

	private canvasContainer!: HTMLElement;
	private claudiaIntegration: ClaudiaCanvasIntegration;

	constructor(
		options: IViewPaneOptions,
		@IKeybindingService keybindingService: IKeybindingService,
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService configurationService: IConfigurationService,
		@IContextKeyService contextKeyService: IContextKeyService,
		@IViewDescriptorService viewDescriptorService: IViewDescriptorService,
		@IInstantiationService instantiationService: IInstantiationService,
		@IOpenerService openerService: IOpenerService,
		@IThemeService themeService: IThemeService,
		@ITelemetryService telemetryService: ITelemetryService,
		@ICommandService private readonly commandService: ICommandService
	) {
		super(options, keybindingService, contextMenuService, configurationService, contextKeyService, viewDescriptorService, instantiationService, openerService, themeService, telemetryService);

		this.claudiaIntegration = new ClaudiaCanvasIntegration();
	}

	protected override renderBody(container: HTMLElement): void {
		super.renderBody(container);

		this.canvasContainer = container;
		this.createCanvasInterface();
	}

	private createCanvasInterface(): void {
		// Create Figma-like canvas interface
		const canvasWrapper = document.createElement('div');
		canvasWrapper.className = 'aura-canvas-wrapper';
		canvasWrapper.style.cssText = `
			width: 100%;
			height: 100%;
			position: relative;
			background: #1e1e1e;
			overflow: hidden;
		`;

		// Create canvas toolbar
		const toolbar = document.createElement('div');
		toolbar.className = 'aura-canvas-toolbar';
		toolbar.style.cssText = `
			position: absolute;
			top: 10px;
			left: 10px;
			right: 10px;
			height: 40px;
			background: #2d2d30;
			border-radius: 6px;
			display: flex;
			align-items: center;
			padding: 0 12px;
			gap: 8px;
			z-index: 100;
		`;

		// Add toolbar buttons
		const addComponentBtn = this.createToolbarButton('Add Component', '$(add)', () => {
			this.commandService.executeCommand('aura.canvas.addComponent');
		});

		const viewModeBtn = this.createToolbarButton('View Mode', '$(layout)', () => {
			this.commandService.executeCommand('aura.canvas.toggleViewMode');
		});

		const agentsBtn = this.createToolbarButton('Agents', '$(robot)', () => {
			this.commandService.executeCommand('aura.agents.show');
		});

		toolbar.appendChild(addComponentBtn);
		toolbar.appendChild(viewModeBtn);
		toolbar.appendChild(agentsBtn);

		// Create canvas area
		const canvasArea = document.createElement('div');
		canvasArea.className = 'aura-canvas-area';
		canvasArea.style.cssText = `
			width: 100%;
			height: 100%;
			position: relative;
			padding-top: 60px;
		`;

		// Initialize Claudia canvas integration
		this.claudiaIntegration.initialize(canvasArea);

		canvasWrapper.appendChild(toolbar);
		canvasWrapper.appendChild(canvasArea);
		this.canvasContainer.appendChild(canvasWrapper);
	}

	private createToolbarButton(title: string, icon: string, onClick: () => void): HTMLElement {
		const button = document.createElement('button');
		button.className = 'aura-toolbar-button';
		button.title = title;
		button.style.cssText = `
			background: transparent;
			border: 1px solid #464647;
			color: #cccccc;
			padding: 6px 12px;
			border-radius: 4px;
			cursor: pointer;
			display: flex;
			align-items: center;
			gap: 4px;
			font-size: 12px;
		`;

		button.innerHTML = `<span class="codicon ${icon}"></span> ${title}`;
		button.addEventListener('click', onClick);

		// Hover effects
		button.addEventListener('mouseenter', () => {
			button.style.background = '#37373d';
		});

		button.addEventListener('mouseleave', () => {
			button.style.background = 'transparent';
		});

		return button;
	}

	override focus(): void {
		super.focus();
		this.canvasContainer?.focus();
	}
}

// Claudia Canvas Integration
class ClaudiaCanvasIntegration extends Disposable {
	private canvasElement!: HTMLElement;
	private componentFrames: Map<string, ComponentFrame> = new Map();

	initialize(container: HTMLElement): void {
		this.canvasElement = container;
		this.setupCanvas();
		this.loadExistingComponents();
	}

	private setupCanvas(): void {
		// Create scrollable canvas with zoom/pan capabilities
		const canvas = document.createElement('div');
		canvas.className = 'claudia-canvas';
		canvas.style.cssText = `
			width: 200%;
			height: 200%;
			position: relative;
			background-image: 
				radial-gradient(circle, #333 1px, transparent 1px);
			background-size: 20px 20px;
			transform-origin: 0 0;
		`;

		this.canvasElement.appendChild(canvas);
	}

	private async loadExistingComponents(): Promise<void> {
		// Load existing component sessions from Claudia
		// This will integrate with the actual Claudia backend
		const mockComponents = [
			{
				id: 'comp-1',
				name: 'NBA Dashboard',
				position: { x: 100, y: 100 },
				size: { width: 300, height: 200 },
				status: 'ready',
				agent: 'Claude-Sonnet'
			},
			{
				id: 'comp-2',
				name: 'Player Stats',
				position: { x: 450, y: 100 },
				size: { width: 280, height: 180 },
				status: 'developing',
				agent: 'GPT-4o'
			}
		];

		for (const comp of mockComponents) {
			this.createComponentFrame(comp);
		}
	}

	private createComponentFrame(component: any): void {
		const frame = document.createElement('div');
		frame.className = 'component-frame';
		frame.style.cssText = `
			position: absolute;
			left: ${component.position.x}px;
			top: ${component.position.y}px;
			width: ${component.size.width}px;
			height: ${component.size.height}px;
			background: #2d2d30;
			border: 2px solid ${component.status === 'ready' ? '#4CAF50' : '#FF9800'};
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.2s ease;
		`;

		// Component header
		const header = document.createElement('div');
		header.style.cssText = `
			padding: 8px 12px;
			background: #37373d;
			border-radius: 6px 6px 0 0;
			font-size: 12px;
			font-weight: 600;
			color: #cccccc;
			display: flex;
			justify-content: space-between;
			align-items: center;
		`;

		header.innerHTML = `
			<span>${component.name}</span>
			<span style="font-size: 10px; opacity: 0.7;">${component.agent}</span>
		`;

		// Component preview area
		const preview = document.createElement('div');
		preview.style.cssText = `
			padding: 12px;
			height: calc(100% - 40px);
			display: flex;
			align-items: center;
			justify-content: center;
			color: #888;
			font-size: 11px;
		`;

		preview.textContent = 'Component Preview';

		frame.appendChild(header);
		frame.appendChild(preview);

		// Add hover effects
		frame.addEventListener('mouseenter', () => {
			frame.style.transform = 'scale(1.02)';
			frame.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
		});

		frame.addEventListener('mouseleave', () => {
			frame.style.transform = 'scale(1)';
			frame.style.boxShadow = 'none';
		});

		// Add click handler
		frame.addEventListener('click', () => {
			this.openComponentDetail(component.id);
		});

		this.canvasElement.querySelector('.claudia-canvas')?.appendChild(frame);
		this.componentFrames.set(component.id, { element: frame, data: component });
	}

	private openComponentDetail(componentId: string): void {
		// Open component detail view
		// This will integrate with Claudia's session management
		console.log('Opening component detail for:', componentId);
	}
}

interface ComponentFrame {
	element: HTMLElement;
	data: any;
}
