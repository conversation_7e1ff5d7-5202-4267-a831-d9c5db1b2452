/*---------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Registry } from 'vs/platform/registry/common/platform';
import { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions } from 'vs/workbench/common/contributions';
import { LifecyclePhase } from 'vs/workbench/services/lifecycle/common/lifecycle';
import { IViewContainersRegistry, Extensions as ViewContainerExtensions, ViewContainerLocation } from 'vs/workbench/common/views';
import { SyncDescriptor } from 'vs/platform/instantiation/common/descriptors';
import { ViewPaneContainer } from 'vs/workbench/browser/parts/views/viewPaneContainer';
import { IConfigurationRegistry, Extensions as ConfigurationExtensions } from 'vs/platform/configuration/common/configurationRegistry';
import { localize } from 'vs/nls';

// Import Aura components
import { AuraCanvasViewPane } from './canvas/auraCanvasView';
import { AuraTimelineViewPane } from './timeline/auraTimelineView';
import { AuraAgentChatViewPane } from './agents/auraAgentChatView';
import { AuraMemoryViewPane } from './memory/auraMemoryView';
import { AuraOrchestrator } from './orchestrator/auraOrchestrator';

// Register Aura view container
const AURA_CONTAINER_ID = 'workbench.view.aura';

Registry.as<IViewContainersRegistry>(ViewContainerExtensions.ViewContainersRegistry).registerViewContainer({
	id: AURA_CONTAINER_ID,
	title: localize('aura', "Aura AI IDE"),
	icon: '$(robot)',
	order: 5,
	ctorDescriptor: new SyncDescriptor(ViewPaneContainer, [AURA_CONTAINER_ID, { mergeViewWithContainerWhenSingleView: true }]),
	storageId: AURA_CONTAINER_ID,
	hideIfEmpty: true,
}, ViewContainerLocation.Sidebar);

// Register Aura views
Registry.as<IViewContainersRegistry>(ViewContainerExtensions.ViewContainersRegistry).registerViews([
	{
		id: 'aura.canvas',
		name: localize('aura.canvas', "Component Canvas"),
		ctorDescriptor: new SyncDescriptor(AuraCanvasViewPane),
		canToggleVisibility: true,
		canMoveView: true,
		containerIcon: '$(layout)',
		order: 1
	},
	{
		id: 'aura.agents',
		name: localize('aura.agents', "AI Agents"),
		ctorDescriptor: new SyncDescriptor(AuraAgentChatViewPane),
		canToggleVisibility: true,
		canMoveView: true,
		containerIcon: '$(robot)',
		order: 2
	},
	{
		id: 'aura.timeline',
		name: localize('aura.timeline', "Universal Timeline"),
		ctorDescriptor: new SyncDescriptor(AuraTimelineViewPane),
		canToggleVisibility: true,
		canMoveView: true,
		containerIcon: '$(history)',
		order: 3
	},
	{
		id: 'aura.memory',
		name: localize('aura.memory', "Memory System"),
		ctorDescriptor: new SyncDescriptor(AuraMemoryViewPane),
		canToggleVisibility: true,
		canMoveView: true,
		containerIcon: '$(database)',
		order: 4
	}
], AURA_CONTAINER_ID);

// Register Aura orchestrator as workbench contribution
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
	AuraOrchestrator,
	LifecyclePhase.Restored
);

// Register Aura configuration
Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration).registerConfiguration({
	id: 'aura',
	title: localize('aura.configuration.title', "Aura AI IDE"),
	type: 'object',
	properties: {
		'aura.enabled': {
			type: 'boolean',
			default: true,
			description: localize('aura.enabled', "Enable Aura AI IDE features")
		},
		'aura.memory.vectorDB.enabled': {
			type: 'boolean',
			default: true,
			description: localize('aura.memory.vectorDB.enabled', "Enable vector database for semantic memory")
		},
		'aura.memory.knowledgeGraph.enabled': {
			type: 'boolean',
			default: true,
			description: localize('aura.memory.knowledgeGraph.enabled', "Enable knowledge graph for relational memory")
		},
		'aura.agents.multiAgent.enabled': {
			type: 'boolean',
			default: true,
			description: localize('aura.agents.multiAgent.enabled', "Enable multi-agent orchestration")
		},
		'aura.canvas.enabled': {
			type: 'boolean',
			default: true,
			description: localize('aura.canvas.enabled', "Enable component canvas view")
		},
		'aura.timeline.universalCheckpoints': {
			type: 'boolean',
			default: true,
			description: localize('aura.timeline.universalCheckpoints', "Enable universal timeline and checkpoints")
		}
	}
});
