# Aura IDE - Claudia Integration Script
# This script integrates Claudia GUI framework into the VS Code fork

Write-Host "🚀 Starting Aura IDE - Claudia Integration..." -ForegroundColor Green

# Check if we're in the right directory
if (!(Test-Path "src/vs/workbench")) {
    Write-Host "❌ Error: Please run this script from the VS Code fork root directory" -ForegroundColor Red
    exit 1
}

# Step 1: Add <PERSON> as a remote and fetch
Write-Host "📡 Adding Claudia remote repository..." -ForegroundColor Yellow
try {
    git remote add claudia https://github.com/getAsterisk/claudia.git 2>$null
    Write-Host "✅ Claudia remote added" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Claudia remote already exists, continuing..." -ForegroundColor Yellow
}

Write-Host "📥 Fetching Claudia code..." -ForegroundColor Yellow
git fetch claudia

# Step 2: Create Aura directory structure
Write-Host "📁 Creating Aura directory structure..." -ForegroundColor Yellow
$auraPath = "src/vs/workbench/contrib/aura"
New-Item -ItemType Directory -Force -Path "$auraPath/canvas" | Out-Null
New-Item -ItemType Directory -Force -Path "$auraPath/agents" | Out-Null
New-Item -ItemType Directory -Force -Path "$auraPath/timeline" | Out-Null
New-Item -ItemType Directory -Force -Path "$auraPath/memory" | Out-Null
New-Item -ItemType Directory -Force -Path "$auraPath/orchestrator" | Out-Null
New-Item -ItemType Directory -Force -Path "$auraPath/claudia" | Out-Null

# Step 3: Add Claudia as subtree
Write-Host "🌳 Adding Claudia as subtree..." -ForegroundColor Yellow
try {
    git subtree add --prefix="$auraPath/claudia" claudia main --squash
    Write-Host "✅ Claudia subtree added successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Claudia subtree may already exist, continuing..." -ForegroundColor Yellow
}

# Step 4: Create package.json for Aura dependencies
Write-Host "📦 Creating Aura package.json..." -ForegroundColor Yellow
$auraPackageJson = @{
    name = "aura-ide"
    version = "1.0.0"
    description = "Aura AI IDE - Advanced AI-powered development environment"
    dependencies = @{
        "@neo4j/driver" = "^5.15.0"
        "pg" = "^8.11.3"
        "pgvector" = "^0.1.8"
        "openai" = "^4.24.1"
        "@anthropic-ai/sdk" = "^0.17.1"
        "@google/generative-ai" = "^0.2.1"
        "react" = "^18.2.0"
        "react-dom" = "^18.2.0"
        "fabric" = "^5.3.0"
        "konva" = "^9.2.0"
        "react-konva" = "^18.2.10"
    }
    devDependencies = @{
        "@types/pg" = "^8.10.9"
        "@types/react" = "^18.2.45"
        "@types/react-dom" = "^18.2.18"
    }
} | ConvertTo-Json -Depth 3

$auraPackageJson | Out-File -FilePath "$auraPath/package.json" -Encoding UTF8

# Step 5: Create TypeScript configuration
Write-Host "⚙️  Creating TypeScript configuration..." -ForegroundColor Yellow
$tsConfig = @{
    compilerOptions = @{
        target = "ES2020"
        module = "ESNext"
        moduleResolution = "node"
        strict = true
        esModuleInterop = true
        skipLibCheck = true
        forceConsistentCasingInFileNames = true
        declaration = true
        outDir = "./dist"
        rootDir = "./src"
        jsx = "react-jsx"
    }
    include = @("src/**/*")
    exclude = @("node_modules", "dist")
} | ConvertTo-Json -Depth 3

$tsConfig | Out-File -FilePath "$auraPath/tsconfig.json" -Encoding UTF8

# Step 6: Create environment configuration
Write-Host "🔧 Creating environment configuration..." -ForegroundColor Yellow
$envTemplate = @"
# Aura AI IDE Configuration
# Copy this to .env and fill in your values

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/aura_db
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# AI Provider API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key
GROK_API_KEY=your_grok_key

# Task Master Integration
TASK_MASTER_API_KEY=your_task_master_key
TASK_MASTER_PROJECT_ID=aura-ide

# Zen MCP Server Configuration
ZEN_MCP_ENABLED=true
ZEN_MCP_PORT=3001

# Aura Features
AURA_MEMORY_ENABLED=true
AURA_CANVAS_ENABLED=true
AURA_MULTI_AGENT_ENABLED=true
AURA_TIMELINE_ENABLED=true
"@

$envTemplate | Out-File -FilePath "$auraPath/.env.template" -Encoding UTF8

# Step 7: Install dependencies
Write-Host "📦 Installing Aura dependencies..." -ForegroundColor Yellow
Push-Location $auraPath
try {
    if (Get-Command bun -ErrorAction SilentlyContinue) {
        bun install
        Write-Host "✅ Dependencies installed with Bun" -ForegroundColor Green
    } elseif (Get-Command npm -ErrorAction SilentlyContinue) {
        npm install
        Write-Host "✅ Dependencies installed with npm" -ForegroundColor Green
    } else {
        Write-Host "⚠️  No package manager found. Please install dependencies manually." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not install dependencies automatically. Please run 'bun install' or 'npm install' in $auraPath" -ForegroundColor Yellow
} finally {
    Pop-Location
}

# Step 8: Create build integration
Write-Host "🔨 Creating build integration..." -ForegroundColor Yellow
$buildScript = @"
# Aura Build Script
# Add this to your main VS Code build process

echo "Building Aura AI IDE components..."
cd src/vs/workbench/contrib/aura
bun run build
echo "Aura build complete!"
"@

$buildScript | Out-File -FilePath "scripts/build-aura.sh" -Encoding UTF8

Write-Host ""
Write-Host "🎉 Aura IDE - Claudia Integration Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Copy $auraPath/.env.template to $auraPath/.env and configure your API keys" -ForegroundColor White
Write-Host "2. Set up your databases (PostgreSQL with PGVector and Neo4j)" -ForegroundColor White
Write-Host "3. Run the VS Code build process to compile Aura components" -ForegroundColor White
Write-Host "4. Start developing your AI-powered IDE!" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Welcome to the future of AI-assisted development!" -ForegroundColor Magenta
